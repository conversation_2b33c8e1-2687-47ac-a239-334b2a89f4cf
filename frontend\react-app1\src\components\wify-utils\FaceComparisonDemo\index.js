import React, { useState } from 'react';
import {
    Upload,
    <PERSON><PERSON>,
    Card,
    Row,
    Col,
    Alert,
    Spin,
    Progress,
    Typography,
    Divider,
} from 'antd';
import {
    InboxOutlined,
    CompareOutlined,
    DeleteOutlined,
} from '@ant-design/icons';
import {
    RekognitionClient,
    CompareFacesCommand,
} from '@aws-sdk/client-rekognition';
import { AWS_CONFIG, REKOGNITION_CONFIG } from './awsConfig';

const { Dragger } = Upload;
const { Title, Text } = Typography;

const FaceComparisonDemo = () => {
    const [sourceImage, setSourceImage] = useState(null);
    const [targetImage, setTargetImage] = useState(null);
    const [loading, setLoading] = useState(false);
    const [result, setResult] = useState(null);
    const [error, setError] = useState(null);

    // AWS Configuration
    const rekognitionClient = new RekognitionClient(AWS_CONFIG);

    // Convert file to base64
    const fileToBase64 = (file) => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => {
                // Remove the data:image/jpeg;base64, prefix
                const base64 = reader.result.split(',')[1];
                resolve(base64);
            };
            reader.onerror = (error) => reject(error);
        });
    };

    // Handle file upload
    const handleUpload = (file, type) => {
        const isImage = file.type.startsWith('image/');
        if (!isImage) {
            setError('Please upload an image file');
            return false;
        }

        const isLt10M = file.size / 1024 / 1024 < 10;
        if (!isLt10M) {
            setError('Image must be smaller than 10MB');
            return false;
        }

        if (type === 'source') {
            setSourceImage(file);
        } else {
            setTargetImage(file);
        }

        setError(null);
        setResult(null);
        return false; // Prevent default upload
    };

    // Compare faces using AWS Rekognition
    const compareFaces = async () => {
        if (!sourceImage || !targetImage) {
            setError('Please upload both images');
            return;
        }

        setLoading(true);
        setError(null);
        setResult(null);

        try {
            // Convert images to base64
            const sourceBase64 = await fileToBase64(sourceImage);
            const targetBase64 = await fileToBase64(targetImage);

            // Convert base64 to Uint8Array
            const sourceBytes = Uint8Array.from(atob(sourceBase64), (c) =>
                c.charCodeAt(0)
            );
            const targetBytes = Uint8Array.from(atob(targetBase64), (c) =>
                c.charCodeAt(0)
            );

            // Create the command
            const command = new CompareFacesCommand({
                SourceImage: {
                    Bytes: sourceBytes,
                },
                TargetImage: {
                    Bytes: targetBytes,
                },
                SimilarityThreshold: REKOGNITION_CONFIG.similarityThreshold,
            });

            // Execute the command
            const response = await rekognitionClient.send(command);

            setResult(response);
        } catch (err) {
            console.error('Error comparing faces:', err);
            setError(`Error: ${err.message}`);
        } finally {
            setLoading(false);
        }
    };

    // Clear all data
    const clearAll = () => {
        setSourceImage(null);
        setTargetImage(null);
        setResult(null);
        setError(null);
    };

    // Render comparison result
    const renderResult = () => {
        if (!result) return null;

        const { FaceMatches, UnmatchedFaces } = result;
        const hasMatches = FaceMatches && FaceMatches.length > 0;

        return (
            <Card title="Comparison Result" style={{ marginTop: 16 }}>
                {hasMatches ? (
                    <div>
                        <Alert
                            message="Face Match Found!"
                            description={`Found ${FaceMatches.length} matching face(s)`}
                            type="success"
                            showIcon
                            style={{ marginBottom: 16 }}
                        />
                        {FaceMatches.map((match, index) => (
                            <div key={index} style={{ marginBottom: 16 }}>
                                <Title level={5}>Match {index + 1}</Title>
                                <Progress
                                    percent={Math.round(match.Similarity)}
                                    status="active"
                                    strokeColor={{
                                        '0%': '#108ee9',
                                        '100%': '#87d068',
                                    }}
                                />
                                <Text>
                                    Similarity: {match.Similarity.toFixed(2)}%
                                </Text>
                                <Divider />
                                <Text type="secondary">
                                    Face Position: Left:{' '}
                                    {match.Face.BoundingBox.Left.toFixed(3)},
                                    Top: {match.Face.BoundingBox.Top.toFixed(3)}
                                    , Width:{' '}
                                    {match.Face.BoundingBox.Width.toFixed(3)},
                                    Height:{' '}
                                    {match.Face.BoundingBox.Height.toFixed(3)}
                                </Text>
                            </div>
                        ))}
                    </div>
                ) : (
                    <Alert
                        message="No Face Match"
                        description="No matching faces found between the two images"
                        type="warning"
                        showIcon
                    />
                )}

                {UnmatchedFaces && UnmatchedFaces.length > 0 && (
                    <div style={{ marginTop: 16 }}>
                        <Title level={5}>Unmatched Faces in Target Image</Title>
                        <Text>
                            Found {UnmatchedFaces.length} face(s) that don't
                            match the source
                        </Text>
                    </div>
                )}
            </Card>
        );
    };

    return (
        <div style={{ padding: 24 }}>
            <Title level={2}>AWS Rekognition Face Comparison Demo</Title>
            <Text type="secondary">
                Upload two images to compare faces using AWS Rekognition service
            </Text>

            {error && (
                <Alert
                    message="Error"
                    description={error}
                    type="error"
                    closable
                    style={{ marginTop: 16, marginBottom: 16 }}
                    onClose={() => setError(null)}
                />
            )}

            <Row gutter={16} style={{ marginTop: 24 }}>
                <Col span={12}>
                    <Card title="Source Image" style={{ height: 300 }}>
                        <Dragger
                            accept="image/*"
                            beforeUpload={(file) =>
                                handleUpload(file, 'source')
                            }
                            showUploadList={false}
                            style={{ height: 200 }}
                        >
                            {sourceImage ? (
                                <div>
                                    <img
                                        src={URL.createObjectURL(sourceImage)}
                                        alt="Source"
                                        style={{
                                            maxWidth: '100%',
                                            maxHeight: 150,
                                        }}
                                    />
                                    <p>{sourceImage.name}</p>
                                </div>
                            ) : (
                                <div>
                                    <p className="ant-upload-drag-icon">
                                        <InboxOutlined />
                                    </p>
                                    <p className="ant-upload-text">
                                        Click or drag source image here
                                    </p>
                                </div>
                            )}
                        </Dragger>
                    </Card>
                </Col>

                <Col span={12}>
                    <Card title="Target Image" style={{ height: 300 }}>
                        <Dragger
                            accept="image/*"
                            beforeUpload={(file) =>
                                handleUpload(file, 'target')
                            }
                            showUploadList={false}
                            style={{ height: 200 }}
                        >
                            {targetImage ? (
                                <div>
                                    <img
                                        src={URL.createObjectURL(targetImage)}
                                        alt="Target"
                                        style={{
                                            maxWidth: '100%',
                                            maxHeight: 150,
                                        }}
                                    />
                                    <p>{targetImage.name}</p>
                                </div>
                            ) : (
                                <div>
                                    <p className="ant-upload-drag-icon">
                                        <InboxOutlined />
                                    </p>
                                    <p className="ant-upload-text">
                                        Click or drag target image here
                                    </p>
                                </div>
                            )}
                        </Dragger>
                    </Card>
                </Col>
            </Row>

            <div style={{ textAlign: 'center', marginTop: 24 }}>
                <Button
                    type="primary"
                    icon={<CompareOutlined />}
                    onClick={compareFaces}
                    loading={loading}
                    disabled={!sourceImage || !targetImage}
                    size="large"
                    style={{ marginRight: 16 }}
                >
                    {loading ? 'Comparing Faces...' : 'Compare Faces'}
                </Button>

                <Button
                    icon={<DeleteOutlined />}
                    onClick={clearAll}
                    size="large"
                >
                    Clear All
                </Button>
            </div>

            {loading && (
                <div style={{ textAlign: 'center', marginTop: 24 }}>
                    <Spin size="large" />
                    <p style={{ marginTop: 16 }}>
                        Processing images with AWS Rekognition...
                    </p>
                </div>
            )}

            {renderResult()}
        </div>
    );
};

export default FaceComparisonDemo;
