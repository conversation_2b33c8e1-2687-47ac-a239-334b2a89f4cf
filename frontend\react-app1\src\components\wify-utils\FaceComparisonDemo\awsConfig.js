// AWS Configuration for Rekognition
// IMPORTANT: Never commit real AWS credentials to version control!

export const AWS_CONFIG = {
    region: process.env.S3_REGION || 'us-east-1',
    credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY || 'YOUR_ACCESS_KEY_ID',
        secretAccessKey: process.env.S3_SECRET_KEY || 'YOUR_SECRET_ACCESS_KEY',
    },
};

// Alternative: Use AWS Cognito Identity Pool for better security
export const COGNITO_CONFIG = {
    region: process.env.S3_REGION || 'us-east-1',
    credentials: {
        identityPoolId:
            process.env.REACT_APP_COGNITO_IDENTITY_POOL_ID ||
            'YOUR_IDENTITY_POOL_ID',
    },
};

// Rekognition specific settings
export const REKOGNITION_CONFIG = {
    similarityThreshold: 70, // Minimum similarity percentage (0-100)
    maxFaces: 10, // Maximum number of faces to detect
    qualityFilter: 'AUTO', // AUTO, LOW, MEDIUM, HIGH
};

/*
SETUP INSTRUCTIONS:

1. Create a .env file in your project root with:
   REACT_APP_AWS_REGION=us-east-1
   REACT_APP_AWS_ACCESS_KEY_ID=your_access_key_here
   REACT_APP_AWS_SECRET_ACCESS_KEY=your_secret_key_here

2. For production, use AWS Cognito Identity Pool instead of direct credentials:
   REACT_APP_COGNITO_IDENTITY_POOL_ID=your_identity_pool_id

3. AWS IAM Permissions needed:
   - rekognition:CompareFaces
   - rekognition:DetectFaces (optional, for additional features)

4. Example IAM Policy:
   {
     "Version": "2012-10-17",
     "Statement": [
       {
         "Effect": "Allow",
         "Action": [
           "rekognition:CompareFaces",
           "rekognition:DetectFaces"
         ],
         "Resource": "*"
       }
     ]
   }
*/
