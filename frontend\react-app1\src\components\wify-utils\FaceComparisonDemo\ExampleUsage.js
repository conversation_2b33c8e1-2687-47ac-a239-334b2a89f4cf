import React from 'react';
import { Card, Typography, Steps, Alert } from 'antd';
import FaceComparisonDemo from './index';

const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;

const ExampleUsage = () => {
    return (
        <div style={{ padding: 24 }}>
            <Card>
                <Title level={2}>
                    Face Comparison Demo - Setup Instructions
                </Title>

                <Alert
                    message="Important Security Note"
                    description="Never commit AWS credentials to version control. Use environment variables or AWS Cognito Identity Pool for production."
                    type="warning"
                    showIcon
                    style={{ marginBottom: 24 }}
                />

                <Title level={3}>Setup Steps:</Title>
                <Steps direction="vertical" current={-1}>
                    <Step
                        title="Create AWS Account & IAM User"
                        description={
                            <div>
                                <Paragraph>
                                    1. Create an AWS account if you don't have
                                    one
                                    <br />
                                    2. Go to IAM console and create a new user
                                    <br />
                                    3. Attach the following policy to the user:
                                </Paragraph>
                                <pre
                                    style={{
                                        background: '#f5f5f5',
                                        padding: 12,
                                        borderRadius: 4,
                                    }}
                                >
                                    {`{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "rekognition:CompareFaces",
        "rekognition:DetectFaces"
      ],
      "Resource": "*"
    }
  ]
}`}
                                </pre>
                            </div>
                        }
                    />

                    <Step
                        title="Configure Environment Variables"
                        description={
                            <div>
                                <Paragraph>
                                    Create a <Text code>.env</Text> file in your
                                    project root:
                                </Paragraph>
                                <pre
                                    style={{
                                        background: '#f5f5f5',
                                        padding: 12,
                                        borderRadius: 4,
                                    }}
                                >
                                    {`REACT_APP_AWS_REGION=us-east-1
REACT_APP_AWS_ACCESS_KEY_ID=your_access_key_here
REACT_APP_AWS_SECRET_ACCESS_KEY=your_secret_key_here`}
                                </pre>
                            </div>
                        }
                    />

                    <Step
                        title="Test the Component"
                        description="Upload two images with faces and click 'Compare Faces' to test the functionality."
                    />
                </Steps>

                <Title level={3} style={{ marginTop: 32 }}>
                    Features:
                </Title>
                <ul>
                    <li>✅ Upload two images for comparison</li>
                    <li>✅ Real-time face comparison using AWS Rekognition</li>
                    <li>✅ Similarity percentage display</li>
                    <li>✅ Face bounding box information</li>
                    <li>✅ Error handling and loading states</li>
                    <li>✅ Support for multiple face matches</li>
                    <li>✅ Configurable similarity threshold</li>
                </ul>

                <Title level={3}>Usage in Your Application:</Title>
                <pre
                    style={{
                        background: '#f5f5f5',
                        padding: 12,
                        borderRadius: 4,
                    }}
                >
                    {`import FaceComparisonDemo from './components/wify-utils/FaceComparisonDemo';

function App() {
  return (
    <div>
      <FaceComparisonDemo />
    </div>
  );
}`}
                </pre>
            </Card>

            <div style={{ marginTop: 24 }}>
                <FaceComparisonDemo />
            </div>
        </div>
    );
};

export default ExampleUsage;
