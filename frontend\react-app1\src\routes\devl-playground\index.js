import {
    <PERSON><PERSON>,
    But<PERSON>,
    Card,
    Form,
    Input,
    message,
    Progress,
    Space,
    Spin,
    Tabs,
} from 'antd';
import React, { Component } from 'react';
import AntdFormBuilder from './AntdFormBuilder';
import ComponentRenderedByString from './ComponentRenderedByString';
import StatusesCreator from '../../components/wify-utils/StatusesCreator';
import { defaultStatuses } from '../../components/wify-utils/StatusesCreator';
import RemoteSourceSelect from '../../components/wify-utils/RemoteSourceSelect';
import S3Uploader from '../../components/wify-utils/S3Uploader/S3Uploader';
import http_utils from '../../util/http_utils';
import MobilePhoneRenderer from './MobilePhoneRenderer';
import BulkUploader from '../../components/wify-utils/BulkUploader';
import VisualJSONRulesEditor from './VisualJSONRulesEditor';
import CSVExporter from '../../components/wify-utils/CSVExporter';
import ComponentRadioButtonOnchage from './ComponentRadioOnChange';
import ComponentUserCustomFiels from './ComponentUserCustomFiels';
import MicInputV2 from '../../components/wify-utils/MicInput_v2';
import Test from '../../components/wify-utils/QRbasedScanner';
import CameraInput from '../../components/wify-utils/CameraInput';
import MetaInputTable from '../../components/wify-utils/MetaInputTable';
import BarcodeScanner from '../../components/wify-utils/BarcodeScanner';
import ExternallyControlledForm from './ExternallyControlledForm';
import FormBuilder from 'antd-form-builder';
import JainishUiDesigns from './JainishUiDesigns';
import RichTextEditorDemo from './RichTextEditorDemo';
import BasicDndDemo from './BasicDnD';
import GlobalSearch from '../../components/WIFY/GlobalSearch';
import SkeletonLoader from '../../components/WIFY/WifyComponents/SkeletonLoader';
import TableLoadingDevPlayground from './TableLoadingDevPlayground';
import { PiCertificate, PiAlignBottom } from 'react-icons/pi';
import { VscGraphLine } from 'react-icons/vsc';
import { LuBrainCircuit } from 'react-icons/lu';
import { AiOutlineApi } from 'react-icons/ai';
import { AiOutlineSisternode } from 'react-icons/ai';
import WarrantyCertificate from './WarrantyCertificate.tsx';
import ProfitAndLossHeader from './ProfitAndLossHeader';
import ProfitAndLossTaskBased from './ProfitAndLossTaskBasedTables';
import ProfitAndLossProjectBasedTables from './ProfitAndLossProjectBasedTables';
import { GiNorthStarShuriken } from 'react-icons/gi';
import AiChatBot from './AiChatBot';
import { RiListCheck2 } from 'react-icons/ri';
import EditableLineItems from './EditableLineItems';
import { isMobileView } from '../../util/helpers';
import FaceComparisonDemo from '../../components/wify-utils/FaceComparisonDemo';
import { FaUserCheck } from 'react-icons/fa';
class DevlPlayground extends Component {
    constructor(props) {
        super(props);
        this.state = {
            s3uploaderReady: true,
            s3Files: [],
        };
    }

    onS3UploaderStatusChange(isReady) {
        // Typically we wil
        this.setState({
            s3uploaderReady: isReady,
        });
        // if(typeof http_utils == "bigint")
        // if(typeof http_utils == "boolean")
        // if(typeof http_utils == "")
        // if(typeof http_utils == "function")
    }

    onS3UploaderFilesChanged(newFiles) {
        // Typically we will save the files in a form
        this.setState({
            s3Files: newFiles,
        });
    }

    getFcmFormMeta() {
        return {
            fields: [
                {
                    key: 'fcm_token',
                    label: 'FCM TOKEN',
                    required: true,
                },
                {
                    key: 'fcm_noti_title',
                    label: 'Notification title',
                    required: true,
                },
                {
                    key: 'fcm_noti_subtitle',
                    label: 'Enter notification body',
                    required: true,
                },
            ],
        };
    }

    sendNotification(data) {
        const onComplete = (resp) => {
            message.success('Notification will receive soon');
        };
        const onError = (error) => {
            message.error('Something went wrong');
        };
        const params = data;
        http_utils.performPostCall(
            '/mock/send-fcm-notification',
            params,
            onComplete,
            onError
        );
    }

    executeProactive() {
        const onComplete = (resp) => {
            message.success(
                'Proactive job scheduled, Requested will be created if rule matched'
            );
        };
        const onError = (error) => {
            message.error('Something went wrong');
        };
        const params = {};
        http_utils.performGetCall(
            '/proactive-requests/create_proactive_request',
            params,
            onComplete,
            onError
        );
    }

    render() {
        // console.log(defaultStatuses);
        return (
            <div>
                <Tabs>
                    <Tabs.TabPane
                        tab={
                            <div className="gx-d-flex gx-align-items-center">
                                <LuBrainCircuit className="gx-fs-2xl gx-bg-white gx-card gx-mb-0 gx-mr-2 gx-border wy-br-5 gx-p-1" />{' '}
                                UI Library
                            </div>
                        }
                        key="item-2"
                    >
                        <Tabs tabPosition={isMobileView() ? 'top' : 'left'}>
                            <Tabs.TabPane
                                tab={
                                    <div className="gx-d-flex gx-align-items-center">
                                        <RiListCheck2 className="gx-fs-2xl gx-bg-white gx-card gx-mb-0 gx-mr-2 gx-border wy-br-5 gx-p-1" />{' '}
                                        Editable Line Items
                                    </div>
                                }
                                key="editable_line_items"
                            >
                                <Card
                                    className="gx-border gx-border-yellow"
                                    title={<h1>Editable Line Items</h1>}
                                >
                                    <EditableLineItems />
                                </Card>
                            </Tabs.TabPane>
                            <Tabs.TabPane
                                tab={
                                    <div className="gx-d-flex gx-align-items-center">
                                        <GiNorthStarShuriken className="gx-fs-2xl gx-bg-white gx-card gx-mb-0 gx-mr-2 gx-border wy-br-5 gx-p-1" />{' '}
                                        Ai Chat bot
                                    </div>
                                }
                                key="ai_chat_bot"
                            >
                                <Card
                                    className="gx-border gx-border-yellow"
                                    title={<h1>AI Chat bot</h1>}
                                >
                                    <AiChatBot />
                                </Card>
                            </Tabs.TabPane>
                            <Tabs.TabPane
                                tab={
                                    <div className="gx-d-flex gx-align-items-center">
                                        <FaUserCheck className="gx-fs-2xl gx-bg-white gx-card gx-mb-0 gx-mr-2 gx-border wy-br-5 gx-p-1" />{' '}
                                        Face Comparison
                                    </div>
                                }
                                key="face_comparison"
                            >
                                <Card
                                    className="gx-border gx-border-success"
                                    title={
                                        <h1>
                                            AWS Rekognition Face Comparison Demo
                                        </h1>
                                    }
                                >
                                    <FaceComparisonDemo />
                                </Card>
                            </Tabs.TabPane>
                            <Tabs.TabPane
                                tab={
                                    <div className="gx-d-flex gx-align-items-center">
                                        <PiAlignBottom className="gx-fs-2xl gx-bg-white gx-card gx-mb-0 gx-mr-2 gx-border wy-br-5 gx-p-1" />{' '}
                                        Skeleton Loading
                                    </div>
                                }
                                key="skeleton_loading"
                            >
                                <Card
                                    className="gx-border gx-border-primary"
                                    title={<h1>Table Skeleton Loading</h1>}
                                >
                                    <Alert
                                        className="gx-fs-18"
                                        message="Table Skeleton Loading cheatsheet"
                                        showIcon
                                        description={
                                            <>
                                                <ul
                                                    className="gx-mb-0 gx-fs-md"
                                                    style={{
                                                        listStyle:
                                                            'lower-latin',
                                                        lineHeight: '1.6',
                                                    }}
                                                >
                                                    <li>
                                                        Enter rows and columns
                                                        for{' '}
                                                        <strong>desktop</strong>
                                                        .
                                                    </li>
                                                    <li>
                                                        Enter rows and columns
                                                        for{' '}
                                                        <strong>mobile</strong>.
                                                    </li>
                                                    <li>
                                                        Select{' '}
                                                        <strong>
                                                            table type
                                                        </strong>
                                                        .
                                                    </li>
                                                    <li>
                                                        Choose if skeleton
                                                        should be{' '}
                                                        <strong>
                                                            scrollable
                                                        </strong>{' '}
                                                        or{' '}
                                                        <strong>
                                                            non-scrollable
                                                        </strong>
                                                        .
                                                    </li>
                                                </ul>
                                            </>
                                        }
                                        closable
                                        type="warning"
                                    />
                                    <TableLoadingDevPlayground />
                                </Card>
                                <Card
                                    className="gx-border gx-border-green"
                                    title={<h1>Skeleton Loading</h1>}
                                >
                                    <SkeletonLoader previewMode={true} />
                                </Card>
                            </Tabs.TabPane>
                            <Tabs.TabPane
                                tab={
                                    <div className="gx-d-flex gx-align-items-center">
                                        <VscGraphLine className="gx-fs-2xl gx-bg-white gx-card gx-mb-0 gx-mr-2 gx-border wy-br-5 gx-p-1" />{' '}
                                        P&L
                                    </div>
                                }
                                key="profit_and_loss"
                            >
                                <Card
                                    className="gx-border gx-border-yellow"
                                    title={<h1>P&L Section</h1>}
                                >
                                    <Tabs>
                                        <Tabs.TabPane
                                            tab={<p>Overall P&L Header</p>}
                                            key="overall"
                                        >
                                            <ProfitAndLossHeader />
                                        </Tabs.TabPane>
                                        <Tabs.TabPane
                                            tab={<p>Task Based P&L Tables</p>}
                                            key="task_based"
                                        >
                                            <ProfitAndLossTaskBased />
                                        </Tabs.TabPane>
                                        <Tabs.TabPane
                                            tab={
                                                <p>Project Based P&L Tables</p>
                                            }
                                            key="project_based"
                                        >
                                            <ProfitAndLossProjectBasedTables />
                                        </Tabs.TabPane>
                                    </Tabs>
                                </Card>
                            </Tabs.TabPane>
                        </Tabs>
                    </Tabs.TabPane>
                    <Tabs.TabPane
                        tab={
                            <div className="gx-d-flex gx-align-items-center">
                                <AiOutlineApi className="gx-fs-2xl gx-card gx-mb-0 gx-bg-white gx-mr-2 gx-border wy-br-5 gx-p-1" />{' '}
                                Legacy
                            </div>
                        }
                        key="item-1"
                    >
                        <Card
                            className="gx-border gx-border-red"
                            title={<h1>GLOBAL SEARCH</h1>}
                        >
                            <GlobalSearch />
                        </Card>
                        <Card
                            className="gx-border gx-border-red"
                            title={<h1>Dnd demo</h1>}
                        >
                            <BasicDndDemo />
                        </Card>
                        <Card
                            className="gx-border gx-border-red"
                            title={<h1>Rich text editor</h1>}
                        >
                            <RichTextEditorDemo />
                        </Card>
                        <Card
                            className="gx-border gx-border-red"
                            title={
                                <h1>
                                    A form that is completely controlled by an
                                    external API
                                </h1>
                            }
                        >
                            <ExternallyControlledForm demoMode />
                        </Card>
                        <Card
                            className="gx-border gx-border-red"
                            title={<h1>Meta Input table</h1>}
                        >
                            <MetaInputTable demoMode edittable />
                        </Card>
                        <Card
                            className="gx-border gx-border-red"
                            title={<h1>Barcode scanner</h1>}
                        >
                            <BarcodeScanner />
                        </Card>
                        <Card
                            className="gx-border gx-border-red"
                            title={<h1>QR scanner</h1>}
                        >
                            <Test />
                        </Card>
                        <Card
                            className="gx-border gx-border-red"
                            title={<h1>MicInputAndPreviewerTest</h1>}
                        >
                            <MicInputV2
                                initialFiles={[]}
                                onReadyStatusChange={(isReady) => {
                                    message.info(
                                        isReady ? 'Ready' : 'Not ready'
                                    );
                                }}
                                // on ready status change
                            />
                        </Card>
                        <Card
                            className="gx-border gx-border-red"
                            title={<h1>Camera Input</h1>}
                        >
                            <CameraInput
                                initialFiles={[]}
                                onReadyStatusChange={(isReady) => {
                                    message.info(
                                        isReady ? 'Ready' : 'Not ready'
                                    );
                                }}
                            />
                        </Card>
                        <Card
                            className="gx-border gx-border-red"
                            title={<h1>CSV Exporter</h1>}
                        >
                            <CSVExporter demoMode submitUrl="/mock" />
                        </Card>
                        <Card
                            className="gx-border gx-border-red"
                            title={<h1>Rule creator</h1>}
                        >
                            <VisualJSONRulesEditor demoMode />
                        </Card>
                        <Card
                            className="gx-border gx-border-red"
                            title={<h1>Bulk uploader</h1>}
                        >
                            <MobilePhoneRenderer
                                viewData={
                                    <>
                                        <div>
                                            <h1>
                                                Content inside the mobile phone
                                            </h1>
                                        </div>
                                    </>
                                }
                            />
                        </Card>
                        <Card
                            className="gx-border gx-border-red"
                            title={<h1>Bulk uploader</h1>}
                        >
                            <BulkUploader
                                debugMode
                                demoMode
                                submitUrl={'/services/modify/15'}
                                onDataModified={(entry_ids) =>
                                    message.success(
                                        'Entries created successfully'
                                    )
                                }
                                // maxRows={4}
                                // dataProto = {dataProtoFrBulkUpload}
                            />
                        </Card>
                        <Card
                            className="gx-border gx-border-red"
                            title={<h1>Ant Form Builder Demo</h1>}
                        >
                            <AntdFormBuilder />
                        </Card>
                        <Card
                            title={<h1>S3 uploader</h1>}
                            className="gx-border gx-border-green"
                        >
                            {!this.state.s3uploaderReady && <Spin></Spin>}
                            {this.state.s3Files.length > 0 && (
                                <Alert
                                    message={JSON.stringify(this.state.s3Files)}
                                    type="info"
                                />
                            )}

                            <S3Uploader
                                // className="gx-w-50"
                                demoMode
                                authToken={http_utils.getAuthToken()}
                                prefixDomain={http_utils.getCDNDomain()}
                                onFilesChanged={(files) => {
                                    this.onS3UploaderFilesChanged(files);
                                }}
                                onReadyStatusChanged={(isReady) => {
                                    this.onS3UploaderStatusChange(isReady);
                                }}
                                // initialFiles = {
                                //     []
                                // }
                            />
                        </Card>
                        <Card title={<h1>Remote sourced select</h1>}>
                            <RemoteSourceSelect
                                url="/searcher"
                                params={{
                                    srvc_type_id: '15',
                                    fn: 'getServiceRequestLabels',
                                }}
                                widgetProps={{
                                    mode: 'tags',
                                    style: {
                                        width: '100%',
                                    },
                                }}
                                initialOptions={undefined}
                            ></RemoteSourceSelect>
                        </Card>
                        <Card title={<h1>Status Creator</h1>}>
                            <StatusesCreator
                                initStatuses={defaultStatuses}
                                onJSONChanged={(newJson) =>
                                    console.log('New JSOn', newJson)
                                }
                            />
                        </Card>
                        <Card title={<h1>Components rendered from String</h1>}>
                            <ComponentRenderedByString />
                        </Card>
                        <Card
                            title={
                                <h1>
                                    Components Radio Button Onchange Added new
                                    meta!
                                </h1>
                            }
                        >
                            <ComponentRadioButtonOnchage />
                        </Card>
                        <Card title={<h1>Components User custom fields!</h1>}>
                            <ComponentUserCustomFiels />
                        </Card>
                        <Card
                            title={<h1>Diwali Diya's</h1>}
                            className="gx-position-relative"
                        >
                            <JainishUiDesigns />
                        </Card>
                        <Card>
                            <Form
                                className="gx-w-100"
                                onFinish={(data) => {
                                    this.sendNotification(data);
                                }}
                            >
                                <FormBuilder meta={this.getFcmFormMeta()} />
                                <Form.Item>
                                    <Button type="primary" htmlType="submit">
                                        Send Notification
                                    </Button>
                                </Form.Item>
                            </Form>
                        </Card>
                        <Card>
                            <Button
                                type="primary"
                                onClick={() => {
                                    this.executeProactive();
                                }}
                            >
                                Execute proactive
                            </Button>
                        </Card>
                    </Tabs.TabPane>
                    <Tabs.TabPane
                        tab={
                            <div className="gx-d-flex gx-align-items-center">
                                <AiOutlineSisternode className="gx-fs-2xl gx-bg-white gx-card gx-mb-0 gx-mr-2 gx-border wy-br-5 gx-p-1" />{' '}
                                SAAS
                            </div>
                        }
                        key="Welspun Certificate"
                    >
                        <Tabs tabPosition="left">
                            <Tabs.TabPane
                                tab={
                                    <div className="gx-d-flex gx-align-items-center">
                                        <PiCertificate className="gx-fs-2xl gx-bg-white gx-card gx-mb-0 gx-mr-2 gx-border wy-br-5 gx-p-1" />{' '}
                                        Welspun Certificate
                                    </div>
                                }
                                key="Welspun Certificate"
                            >
                                {' '}
                                <WarrantyCertificate />
                            </Tabs.TabPane>
                        </Tabs>
                    </Tabs.TabPane>
                </Tabs>
            </div>
        );
    }
}

export default DevlPlayground;
