# AWS Configuration for Rekognition Face Comparison Demo
# Copy this file to .env and fill in your actual AWS credentials

# AWS Region (choose the region closest to you)
REACT_APP_AWS_REGION=us-east-1

# AWS Credentials (Method 1 - Direct credentials)
# WARNING: Only use this for development/testing. Never commit real credentials!
REACT_APP_AWS_ACCESS_KEY_ID=your_access_key_here
REACT_APP_AWS_SECRET_ACCESS_KEY=your_secret_key_here

# AWS Cognito Identity Pool (Method 2 - Recommended for production)
# REACT_APP_COGNITO_IDENTITY_POOL_ID=your_identity_pool_id

# Instructions:
# 1. Create an AWS account and IAM user with Rekognition permissions
# 2. Copy this file to .env
# 3. Replace the placeholder values with your actual AWS credentials
# 4. Never commit the .env file to version control
# 5. For production, use AWS Cognito Identity Pool instead of direct credentials
