# AWS Rekognition Face Comparison Demo

This component provides a demo interface for comparing faces in two images using AWS Rekognition service.

## Features

- ✅ Upload two images for face comparison
- ✅ Real-time face matching using AWS Rekognition
- ✅ Similarity percentage display with progress bars
- ✅ Face bounding box information
- ✅ Error handling and loading states
- ✅ Support for multiple face matches
- ✅ Configurable similarity threshold
- ✅ Clean Ant Design UI

## Setup Instructions

### 1. AWS Account Setup

1. Create an AWS account if you don't have one
2. Go to the IAM console and create a new user
3. Create and attach the following IAM policy:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "rekognition:CompareFaces",
        "rekognition:DetectFaces"
      ],
      "Resource": "*"
    }
  ]
}
```

4. Generate access keys for the user

### 2. Environment Configuration

Create a `.env` file in your project root:

```env
REACT_APP_AWS_REGION=us-east-1
REACT_APP_AWS_ACCESS_KEY_ID=your_access_key_here
REACT_APP_AWS_SECRET_ACCESS_KEY=your_secret_key_here
```

**Important**: Never commit real AWS credentials to version control!

### 3. For Production (Recommended)

Use AWS Cognito Identity Pool instead of direct credentials:

```env
REACT_APP_AWS_REGION=us-east-1
REACT_APP_COGNITO_IDENTITY_POOL_ID=your_identity_pool_id
```

## Usage

### Basic Usage

```jsx
import FaceComparisonDemo from './components/wify-utils/FaceComparisonDemo';

function App() {
  return (
    <div>
      <FaceComparisonDemo />
    </div>
  );
}
```

### With Example Page

```jsx
import ExampleUsage from './components/wify-utils/FaceComparisonDemo/ExampleUsage';

function App() {
  return (
    <div>
      <ExampleUsage />
    </div>
  );
}
```

## Configuration

You can modify the configuration in `awsConfig.js`:

```javascript
export const REKOGNITION_CONFIG = {
  similarityThreshold: 70, // Minimum similarity percentage (0-100)
  maxFaces: 10, // Maximum number of faces to detect
  qualityFilter: 'AUTO', // AUTO, LOW, MEDIUM, HIGH
};
```

## API Response Structure

The component handles AWS Rekognition's `CompareFaces` response:

```javascript
{
  FaceMatches: [
    {
      Similarity: 99.85,
      Face: {
        BoundingBox: {
          Width: 0.12,
          Height: 0.16,
          Left: 0.42,
          Top: 0.31
        }
      }
    }
  ],
  UnmatchedFaces: [
    // Faces in target image that don't match source
  ]
}
```

## Error Handling

The component handles various error scenarios:
- Invalid file types
- Files too large (>10MB)
- AWS authentication errors
- Network connectivity issues
- No faces detected in images

## File Structure

```
FaceComparisonDemo/
├── index.js          # Main component
├── awsConfig.js       # AWS configuration
├── ExampleUsage.js    # Example usage with instructions
└── README.md          # This file
```

## Testing

1. Upload two images with clear faces
2. Click "Compare Faces"
3. View the similarity results
4. Test with different types of images:
   - Same person, different photos
   - Different people
   - Multiple faces in one image
   - Poor quality images

## Limitations

- Maximum file size: 10MB
- Supported formats: JPEG, PNG
- Works best with clear, front-facing photos
- Requires internet connection for AWS API calls

## Cost Considerations

AWS Rekognition pricing (as of 2024):
- $0.001 per image for face comparison
- First 1,000 images per month are free
- Monitor usage in AWS console

## Security Best Practices

1. Use environment variables for credentials
2. Implement AWS Cognito for production
3. Set up proper IAM roles with minimal permissions
4. Monitor API usage and costs
5. Implement rate limiting if needed

## Troubleshooting

### Common Issues

1. **"Access Denied" Error**
   - Check IAM permissions
   - Verify credentials in .env file

2. **"No faces detected"**
   - Ensure images contain clear faces
   - Try different image formats

3. **Network Errors**
   - Check internet connection
   - Verify AWS region settings

4. **CORS Issues**
   - AWS SDK handles CORS automatically
   - Ensure proper region configuration
